#!/usr/bin/env python3
"""
Database migration script to:
1. Add video/shorts preferences to Configuration table
2. Remove video_approved field from Meme table
3. Drop Clip table
"""

import sqlite3
import os
import sys

def migrate_database():
    """Perform database migration"""
    db_path = os.path.join('instance', 'app.db')
    
    if not os.path.exists(db_path):
        print("Database file not found. Creating new database with updated schema.")
        return True
    
    print(f"Migrating database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if Configuration table exists and get its current structure
        cursor.execute("PRAGMA table_info(configuration)")
        config_columns = [row[1] for row in cursor.fetchall()]
        
        # Add new columns to Configuration table if they don't exist
        if 'create_videos' not in config_columns:
            print("Adding create_videos column to Configuration table...")
            cursor.execute("ALTER TABLE configuration ADD COLUMN create_videos BOOLEAN DEFAULT 1")
        
        if 'create_shorts' not in config_columns:
            print("Adding create_shorts column to Configuration table...")
            cursor.execute("ALTER TABLE configuration ADD COLUMN create_shorts BOOLEAN DEFAULT 0")
        
        # Check if Meme table has video_approved column and remove it
        cursor.execute("PRAGMA table_info(meme)")
        meme_columns = [row[1] for row in cursor.fetchall()]
        
        if 'video_approved' in meme_columns:
            print("Removing video_approved column from Meme table...")
            # SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
            
            # Create new table without video_approved column
            cursor.execute("""
                CREATE TABLE meme_new (
                    id INTEGER PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    url VARCHAR(500) NOT NULL,
                    image_path VARCHAR(500),
                    text TEXT,
                    subreddit VARCHAR(100) NOT NULL,
                    discarded BOOLEAN DEFAULT 0,
                    text_approved BOOLEAN DEFAULT 0,
                    audio_approved BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id)
                )
            """)
            
            # Copy data from old table to new table
            cursor.execute("""
                INSERT INTO meme_new (id, user_id, url, image_path, text, subreddit, 
                                     discarded, text_approved, audio_approved, created_at)
                SELECT id, user_id, url, image_path, text, subreddit, 
                       discarded, text_approved, audio_approved, created_at
                FROM meme
            """)
            
            # Drop old table and rename new table
            cursor.execute("DROP TABLE meme")
            cursor.execute("ALTER TABLE meme_new RENAME TO meme")
        
        # Check if Clip table exists and drop it
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='clip'")
        if cursor.fetchone():
            print("Dropping Clip table...")
            cursor.execute("DROP TABLE clip")
        
        # Commit changes
        conn.commit()
        print("Database migration completed successfully!")
        
        # Verify the changes
        print("\nVerifying migration:")
        
        # Check Configuration table structure
        cursor.execute("PRAGMA table_info(configuration)")
        config_columns = [row[1] for row in cursor.fetchall()]
        print(f"Configuration table columns: {config_columns}")
        
        # Check Meme table structure
        cursor.execute("PRAGMA table_info(meme)")
        meme_columns = [row[1] for row in cursor.fetchall()]
        print(f"Meme table columns: {meme_columns}")
        
        # Check that Clip table is gone
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='clip'")
        clip_exists = cursor.fetchone() is not None
        print(f"Clip table exists: {clip_exists}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error during migration: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    success = migrate_database()
    sys.exit(0 if success else 1)
