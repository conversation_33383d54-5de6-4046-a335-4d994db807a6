<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/config.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/meme_workshop">🎭 Meme Workshop</a>
            <a href="/audio_workshop">🎵 Audio Workshop</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <h1>⚙️ Settings</h1>
        <p>Configure your meme generation preferences and content sources.</p>

        <div class="settings-layout">
            <!-- Left Side: Configuration Form -->
            <div class="settings-form">
                <form method="POST" action="/update_config">
                    <!-- Max Memes Section -->
                    <div class="settings-section">
                        <h3>📊 Content Limits</h3>
                        <div class="form-group">
                            <label for="max_memes">Maximum Memes per Fetch (1-100):</label>
                            <input type="number" id="max_memes" name="max_memes" min="1" max="100"
                                   value="{{ config.max_memes }}" required class="form-input">
                        </div>
                    </div>

                    <!-- Content Types Section -->
                    <div class="settings-section">
                        <h3>🎬 Content Types</h3>
                        <p class="section-description">Select which types of content you want to generate from your memes.</p>
                        <div class="content-types-grid">
                            <div class="content-type-card">
                                <input type="checkbox" id="create_videos" name="create_videos"
                                       {% if config.create_videos %}checked{% endif %}>
                                <label for="create_videos" class="content-type-label">
                                    <span class="content-icon">📹</span>
                                    <span class="content-title">Regular Videos</span>
                                    <span class="content-desc">Standard format videos</span>
                                </label>
                            </div>
                            <div class="content-type-card">
                                <input type="checkbox" id="create_shorts" name="create_shorts"
                                       {% if config.create_shorts %}checked{% endif %}>
                                <label for="create_shorts" class="content-type-label">
                                    <span class="content-icon">📱</span>
                                    <span class="content-title">Shorts</span>
                                    <span class="content-desc">Vertical short-form videos</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Subreddits Section -->
                    <div class="settings-section">
                        <h3>📂 Subreddit Sources</h3>
                        <p class="section-description">Choose which subreddits to fetch memes from.</p>

                        <!-- Available Subreddits -->
                        <div class="subreddits-grid" id="subreddits">
                            {% for subreddit in available_subreddits %}
                            <div class="subreddit-card">
                                <input type="checkbox" id="subreddit_{{ subreddit }}" name="subreddits"
                                       value="{{ subreddit }}" {% if subreddit in selected_subreddits %}checked{% endif %}>
                                <label for="subreddit_{{ subreddit }}" class="subreddit-label">
                                    <span class="subreddit-name">r/{{ subreddit }}</span>
                                    <a href="https://reddit.com/r/{{ subreddit }}" target="_blank" class="subreddit-link">🔗</a>
                                </label>
                            </div>
                            {% endfor %}

                            <!-- Custom Subreddits -->
                            {% for subreddit in selected_subreddits %}
                                {% if subreddit not in available_subreddits %}
                                <div class="subreddit-card custom-subreddit">
                                    <input type="checkbox" id="subreddit_{{ subreddit }}" name="subreddits"
                                           value="{{ subreddit }}" checked>
                                    <label for="subreddit_{{ subreddit }}" class="subreddit-label">
                                        <span class="subreddit-name">r/{{ subreddit }}</span>
                                        <a href="https://reddit.com/r/{{ subreddit }}" target="_blank" class="subreddit-link">🔗</a>
                                        <button type="button" class="remove-subreddit" onclick="removeSubreddit(this)">×</button>
                                    </label>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>

                        <!-- Add New Subreddit -->
                        <div class="add-subreddit-section">
                            <div class="add-subreddit-input">
                                <input type="text" id="new_subreddit" name="new_subreddit"
                                       placeholder="Add subreddit name or URL (e.g., 'memes' or 'https://reddit.com/r/memes')"
                                       class="form-input">
                                <button type="button" onclick="addSubreddit()" class="btn btn-secondary">Add Subreddit</button>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">💾 Save Settings</button>
                        <a href="/dashboard" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>

            <!-- Right Side: Current Configuration Summary -->
            <div class="settings-summary">
                <div class="summary-card">
                    <h3>📋 Current Configuration</h3>

                    <div class="summary-section">
                        <h4>Content Limits</h4>
                        <div class="summary-item">
                            <span class="summary-label">Max Memes per Fetch:</span>
                            <span class="summary-value">{{ config.max_memes }}</span>
                        </div>
                    </div>

                    <div class="summary-section">
                        <h4>Content Types</h4>
                        <div class="summary-tags">
                            {% if config.create_videos %}
                            <span class="summary-tag enabled">📹 Regular Videos</span>
                            {% endif %}
                            {% if config.create_shorts %}
                            <span class="summary-tag enabled">📱 Shorts</span>
                            {% endif %}
                            {% if not config.create_videos and not config.create_shorts %}
                            <span class="summary-tag warning">⚠️ No content types selected</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="summary-section">
                        <h4>Active Subreddits ({{ selected_subreddits|length }})</h4>
                        <div class="summary-tags">
                            {% for subreddit in selected_subreddits %}
                            <span class="summary-tag subreddit">
                                <a href="https://reddit.com/r/{{ subreddit }}" target="_blank" class="subreddit-link">
                                    r/{{ subreddit }}
                                </a>
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addSubreddit() {
            const newSubredditInput = document.getElementById('new_subreddit');
            const subredditList = document.getElementById('subreddits');

            if (newSubredditInput.value.trim() !== '') {
                const inputValue = newSubredditInput.value.trim();

                // Extract subreddit name from URL or use as-is
                let subredditName = inputValue;
                const urlMatch = inputValue.match(/(?:https?:\/\/)?(?:www\.)?reddit\.com\/r\/([a-zA-Z0-9_]+)/);
                if (urlMatch) {
                    subredditName = urlMatch[1];
                } else if (inputValue.startsWith('r/')) {
                    subredditName = inputValue.substring(2);
                }

                // Check if subreddit already exists
                const existingCheckbox = document.getElementById(`subreddit_${subredditName}`);
                if (existingCheckbox) {
                    existingCheckbox.checked = true;
                    newSubredditInput.value = '';
                    return;
                }

                const newDiv = document.createElement('div');
                newDiv.className = 'subreddit-card custom-subreddit';
                newDiv.innerHTML = `
                    <input type="checkbox" id="subreddit_${subredditName}" name="subreddits" value="${subredditName}" checked>
                    <label for="subreddit_${subredditName}" class="subreddit-label">
                        <span class="subreddit-name">r/${subredditName}</span>
                        <a href="https://reddit.com/r/${subredditName}" target="_blank" class="subreddit-link">🔗</a>
                        <button type="button" class="remove-subreddit" onclick="removeSubreddit(this)">×</button>
                    </label>
                `;
                subredditList.appendChild(newDiv);
                newSubredditInput.value = '';
            }
        }

        function removeSubreddit(button) {
            const subredditCard = button.closest('.subreddit-card');
            subredditCard.remove();
        }

        // Allow Enter key to add subreddit
        document.getElementById('new_subreddit').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addSubreddit();
            }
        });
    </script>
</body>
</html>
