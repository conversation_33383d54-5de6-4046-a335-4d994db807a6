<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/config.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/meme_workshop">🎭 Meme Workshop</a>
            <a href="/audio_workshop">🎵 Audio Workshop</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <h2>Update Configuration</h2>
        <form method="POST" action="/update_config">
            <div class="form-group">
                <label for="max_memes">Max Memes (1-100):</label>
                <input type="number" id="max_memes" name="max_memes" min="1" max="100"
                       value="{{ config.max_memes }}" required>
            </div>

            <div class="form-group">
                <label for="subreddits">Subreddits:</label>
                <div id="subreddits">
                    {% for subreddit in available_subreddits %}
                    <div class="subreddit-item">
                        <input type="checkbox" id="subreddit_{{ subreddit }}" name="subreddits"
                               value="{{ subreddit }}" {% if subreddit in selected_subreddits %}checked{% endif %}>
                        <label for="subreddit_{{ subreddit }}">
                            <a href="https://reddit.com/r/{{ subreddit }}" target="_blank" class="subreddit-link">
                                r/{{ subreddit }}
                            </a>
                        </label>
                    </div>
                    {% endfor %}
                    <div class="add-subreddit">
                        <input type="text" id="new_subreddit" name="new_subreddit"
                               placeholder="Add subreddit name or URL (e.g., 'memes' or 'https://reddit.com/r/memes')">
                        <button type="button" onclick="addSubreddit()">Add</button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>Content Types to Generate:</label>
                <div class="content-types">
                    <div class="content-type-item">
                        <input type="checkbox" id="create_videos" name="create_videos"
                               {% if config.create_videos %}checked{% endif %}>
                        <label for="create_videos">📹 Regular Videos</label>
                    </div>
                    <div class="content-type-item">
                        <input type="checkbox" id="create_shorts" name="create_shorts"
                               {% if config.create_shorts %}checked{% endif %}>
                        <label for="create_shorts">📱 Shorts</label>
                    </div>
                </div>
                <small>Select which types of content you want to generate from your memes.</small>
            </div>

            <button type="submit">Save Settings</button>
        </form>

        <div class="current-config">
            <h3>Current Configuration</h3>
            <p><strong>Max Memes:</strong> {{ config.max_memes }}</p>
            <p><strong>Selected Subreddits:</strong></p>
            <div class="current-subreddits">
                {% for subreddit in selected_subreddits %}
                <span class="subreddit-tag">
                    <a href="https://reddit.com/r/{{ subreddit }}" target="_blank" class="subreddit-link">
                        r/{{ subreddit }}
                    </a>
                </span>
                {% endfor %}
            </div>
            <p><strong>Content Types:</strong></p>
            <div class="current-content-types">
                {% if config.create_videos %}
                <span class="content-type-tag">📹 Regular Videos</span>
                {% endif %}
                {% if config.create_shorts %}
                <span class="content-type-tag">📱 Shorts</span>
                {% endif %}
                {% if not config.create_videos and not config.create_shorts %}
                <span class="content-type-tag">⚠️ No content types selected</span>
                {% endif %}
            </div>
        </div>
    </div>
    <script>
        function addSubreddit() {
            const newSubredditInput = document.getElementById('new_subreddit');
            const subredditList = document.getElementById('subreddits');

            if (newSubredditInput.value.trim() !== '') {
                const inputValue = newSubredditInput.value.trim();

                // Extract subreddit name from URL or use as-is
                let subredditName = inputValue;
                const urlMatch = inputValue.match(/(?:https?:\/\/)?(?:www\.)?reddit\.com\/r\/([a-zA-Z0-9_]+)/);
                if (urlMatch) {
                    subredditName = urlMatch[1];
                } else if (inputValue.startsWith('r/')) {
                    subredditName = inputValue.substring(2);
                }

                const newDiv = document.createElement('div');
                newDiv.className = 'subreddit-item';
                newDiv.innerHTML = `
                    <input type="checkbox" id="subreddit_${subredditName}" name="subreddits" value="${subredditName}" checked>
                    <label for="subreddit_${subredditName}">
                        <a href="https://reddit.com/r/${subredditName}" target="_blank" class="subreddit-link">
                            r/${subredditName}
                        </a>
                    </label>
                `;
                subredditList.insertBefore(newDiv, subredditList.lastElementChild);
                newSubredditInput.value = '';
            }
        }
    </script>
</body>
</html>
