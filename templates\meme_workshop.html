<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meme Workshop</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/memes.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/meme_workshop">🎭 Meme Workshop</a>
            <a href="/audio_workshop">🎵 Audio Workshop</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <h1>🎭 Meme Workshop</h1>
        <p>Fetch memes from Reddit and edit them for your content pipeline.</p>

        <!-- Flash Messages -->
        {% if session.flash_message %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
        </div>
        {% endif %}

        <div class="workshop-sections">
            <!-- Fetch Memes Section -->
            <div class="workshop-section">
                <h2>📥 Fetch New Memes</h2>
                <p>Get fresh memes from Reddit subreddits.</p>
                <div class="actions">
                    <a href="/fetch_memes" class="btn btn-primary">🎭 Fetch Memes</a>
                </div>
            </div>

            <!-- Edit Memes Section -->
            <div class="workshop-section">
                <h2>✏️ Edit & Approve Memes</h2>
                <p>Add text for narration and approve memes for the next stage.</p>
                <div class="actions">
                    <a href="/review_memes" class="btn btn-primary">✏️ Edit Memes</a>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="workshop-section">
                <h2>⚡ Quick Actions</h2>
                <div class="actions">
                    <a href="/clear_memes" class="btn btn-danger"
                       onclick="return confirm('Are you sure you want to clear all memes? This cannot be undone.')">
                       🗑️ Clear All Memes
                    </a>
                </div>
            </div>
        </div>

        <div class="navigation-actions">
            <a href="/audio_workshop" class="btn btn-secondary">🎵 Continue to Audio Workshop</a>
            <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
        </div>
    </div>

    <script>
        // Auto-hide flash messages after 5 seconds
        setTimeout(function() {
            const flashMessage = document.querySelector('[style*="background: #d4edda"]');
            if (flashMessage) {
                flashMessage.style.display = 'none';
            }
        }, 5000);
    </script>
</body>
</html>
